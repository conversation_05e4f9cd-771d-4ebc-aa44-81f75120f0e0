
import { AdvancedDataTable } from '@/components/ui/advanced-data-table';
import { Badge } from '@/components/ui/badge';

// Simple test data
const testData = [
  { id: 1, name: '<PERSON>', role: 'Team Lead', status: 'active', events: 4, hours: 24 },
  { id: 2, name: '<PERSON>', role: 'Coordinator', status: 'active', events: 6, hours: 32 },
  { id: 3, name: '<PERSON>', role: 'Volunteer', status: 'pending', events: 2, hours: 8 },
  { id: 4, name: '<PERSON>', role: 'Specialist', status: 'active', events: 5, hours: 28 },
  { id: 5, name: '<PERSON>', role: 'Team Lead', status: 'active', events: 3, hours: 18 },
];

// Simple columns
const testColumns = [
  {
    accessorKey: 'name',
    header: 'Volunteer Name',
    size: 150,
  },
  {
    accessorKey: 'role',
    header: 'Role',
    size: 120,
    cell: ({ getValue }: any) => {
      const value = getValue() as string;
      return (
        <Badge variant={value === 'Team Lead' ? 'grade-a' : value === 'Coordinator' ? 'grade-b' : 'neutral'}>
          {value}
        </Badge>
      );
    }
  },
  {
    accessorKey: 'status',
    header: 'Status',
    size: 100,
    cell: ({ getValue }: any) => {
      const value = getValue() as string;
      return (
        <Badge variant={value === 'active' ? 'grade-a' : 'grade-c'} size="sm">
          {value.toUpperCase()}
        </Badge>
      );
    }
  },
  {
    accessorKey: 'events',
    header: 'Events',
    size: 90,
  },
  {
    accessorKey: 'hours',
    header: 'Hours',
    size: 90,
  },
];

export function TestFrozenColumns() {
  return (
    <div className="p-6 space-y-4">
      <h1 className="text-2xl font-bold">Frozen Columns Test</h1>
      <p className="text-muted-foreground">
        This table should have the first 2 columns (selection + name) frozen when scrolling horizontally.
      </p>
      
      <div className="border rounded-lg overflow-auto" style={{ width: '600px', maxWidth: '600px' }}>
        <div style={{ minWidth: '800px' }}>
          <AdvancedDataTable
            data={testData}
            columns={testColumns}
            selection={{
              enabled: true,
              mode: 'multiple',
              selectedRows: [],
              onSelectionChange: () => {}
            }}
            frozenColumns={{
              count: 2, // Freeze selection + name columns
              shadowIntensity: 'medium'
            }}
            searchable={false}
            pagination={false}
            className="w-full"
          />
        </div>
      </div>
      
      <div className="text-sm text-muted-foreground">
        <p><strong>Expected behavior:</strong></p>
        <ul className="list-disc list-inside space-y-1">
          <li>Selection checkbox column should be sticky at left: 0px</li>
          <li>Name column should be sticky at left: 40px (after selection column)</li>
          <li>Other columns should scroll horizontally</li>
          <li>Frozen columns should have a shadow on the right edge</li>
          <li>Background colors should match the theme</li>
        </ul>
      </div>
    </div>
  );
}

export default TestFrozenColumns;
