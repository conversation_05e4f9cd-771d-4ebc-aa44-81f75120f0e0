@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* shadcn/ui default variables */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
    
    /* Custom theme variables - will be injected by theme system */
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-tertiary: #f3f4f6;
    --bg-header: #ffffff;
    --bg-header-group: #f9fafb;
    --text-primary: #111827;
    --text-secondary: #4b5563;
    --text-header: #111827;
    --border-color: #e5e7eb;
    --border-header: #d1d5db;
    --accent-color: #f59e0b;
    --hover-bg: rgba(245, 158, 11, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    --frozen-shadow: 2px 0 4px rgba(0,0,0,0.05);
    
    /* Badge colors */
    --badge-grade-a: #d1fae5;
    --badge-grade-a-foreground: #065f46;
    --badge-grade-a-border: #a7f3d0;
    --badge-grade-b: #e0f2fe;
    --badge-grade-b-foreground: #0369a1;
    --badge-grade-b-border: #bae6fd;
    --badge-grade-c: #fef3c7;
    --badge-grade-c-foreground: #b45309;
    --badge-grade-c-border: #fde68a;
    --badge-grade-d: #fed7aa;
    --badge-grade-d-foreground: #c2410c;
    --badge-grade-d-border: #fdba74;
    --badge-grade-f: #fee2e2;
    --badge-grade-f-foreground: #dc2626;
    --badge-grade-f-border: #fecaca;
    --badge-neutral: #f3f4f6;
    --badge-neutral-foreground: #374151;
    --badge-neutral-border: #e5e7eb;
    
    /* Typography */
    --font-family-primary: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Inter", sans-serif;
    --font-family-secondary: Inter, sans-serif;
    --font-family-mono: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* Border radius */
    --radius-none: 0;
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-full: 9999px;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  /*
   * .dark class removed - all theme switching now handled dynamically
   * through CSS variable injection in the theme system
   */
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-family-primary);
    transition: background-color 0.3s, color 0.3s;
  }
}

/* Custom utility classes using our theme variables */
@layer utilities {
  .bg-theme-primary {
    background-color: var(--bg-primary);
  }
  
  .bg-theme-secondary {
    background-color: var(--bg-secondary);
  }
  
  .bg-theme-tertiary {
    background-color: var(--bg-tertiary);
  }
  
  .text-theme-primary {
    color: var(--text-primary);
  }
  
  .text-theme-secondary {
    color: var(--text-secondary);
  }
  
  .border-theme {
    border-color: var(--border-color);
  }
  
  .shadow-theme {
    box-shadow: var(--shadow);
  }
  
  .accent-color {
    color: var(--accent-color);
  }
  
  .bg-accent-color {
    background-color: var(--accent-color);
  }

  /* Advanced Data Table Utilities */
  .table-frozen-column {
    position: sticky;
    z-index: 10;
    background-color: inherit;
  }

  .table-frozen-shadow-light {
    box-shadow: 1px 0 3px rgba(0,0,0,0.1);
  }

  .table-frozen-shadow-medium {
    box-shadow: 2px 0 4px rgba(0,0,0,0.15);
  }

  .table-frozen-shadow-heavy {
    box-shadow: 3px 0 6px rgba(0,0,0,0.2);
  }

  /* Mobile touch optimization */
  .table-touch-optimized {
    min-height: 44px;
  }

  .table-touch-optimized td,
  .table-touch-optimized th {
    min-height: 44px;
    padding: 12px 16px;
  }

  /* Responsive table utilities */
  @media (max-width: 768px) {
    .table-mobile-stack {
      display: block;
    }

    .table-mobile-stack thead,
    .table-mobile-stack tbody,
    .table-mobile-stack th,
    .table-mobile-stack td,
    .table-mobile-stack tr {
      display: block;
    }

    .table-mobile-stack thead tr {
      position: absolute;
      top: -9999px;
      left: -9999px;
    }

    .table-mobile-stack tr {
      border: 1px solid var(--border-color);
      margin-bottom: 10px;
      padding: 10px;
      border-radius: 8px;
      background: var(--bg-primary);
    }

    .table-mobile-stack td {
      border: none;
      position: relative;
      padding-left: 50% !important;
      text-align: left;
    }

    .table-mobile-stack td:before {
      content: attr(data-label);
      position: absolute;
      left: 6px;
      width: 45%;
      padding-right: 10px;
      white-space: nowrap;
      font-weight: 600;
      color: var(--text-secondary);
    }
  }
}
